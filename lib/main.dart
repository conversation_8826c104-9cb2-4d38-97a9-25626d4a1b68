import 'package:flutter/material.dart';
import 'package:myapp/demopage.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(debugShowCheckedModeBanner: false, home: HomePage());
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text('AppBar'),
          backgroundColor: Colors.pink,
          centerTitle: true,
          actions: [
            IconButton(onPressed: () {}, icon: Icon(Icons.search)),
            IconButton(onPressed: () {}, icon: Icon(Icons.more_vert)),
          ],
        ),
        drawer: Drawer(
          backgroundColor: Colors.amberAccent,
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.home),
                title: Text('Home'),
                trailing: Icon(Icons.arrow_forward),
              ),

              ListTile(
                leading: Icon(Icons.home),
                title: Text('Settings'),
                trailing: Icon(Icons.settings),
              ),

              ListTile(
                leading: Icon(Icons.home),
                title: Text('LogOut'),
                trailing: Icon(Icons.logout),
              ),
            ],
          ),
        ),
        body: Padding(
          padding: EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Image(
                  image: AssetImage('assets/images/anik.jpg'),
                  width: 200,
                  height: 200,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    minimumSize: Size(double.infinity, 30),
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => DemoPage()),
                    );
                  },
                  child: Text(
                    'Button',
                    style: TextStyle(fontSize: 20, color: Colors.white),
                  ),
                ),
                SizedBox(height: 10),
                Image(
                  image: AssetImage('assets/images/anik.jpg'),
                  width: 200,
                  height: 200,
                ),
                SizedBox(height: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    minimumSize: Size(double.infinity, 30),
                  ),
                  onPressed: () {},
                  child: Text(
                    'Button',
                    style: TextStyle(fontSize: 20, color: Colors.white),
                  ),
                ),
                SizedBox(height: 10),
                Image(
                  image: AssetImage('assets/images/anik.jpg'),
                  width: 200,
                  height: 200,
                ),
                SizedBox(height: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    minimumSize: Size(double.infinity, 30),
                  ),
                  onPressed: () {},
                  child: Text(
                    'Button',
                    style: TextStyle(fontSize: 20, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: BottomNavigationBar(
          backgroundColor: Colors.pink,
          items: [
            BottomNavigationBarItem(
              icon: Icon(Icons.home, color: Colors.white),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings, color: Colors.white),
              label: 'Settings',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.logout, color: Colors.white),
              label: 'LogOut',
            ),
          ],
        ),
      ),
    );
  }
}
