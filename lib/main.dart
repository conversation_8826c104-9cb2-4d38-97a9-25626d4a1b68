import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Gallery App Lite',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
      ),
      home: const GalleryScreen(),
    );
  }
}

/// Main Gallery Screen that displays all media files in a grid
class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  List<AssetEntity> _mediaList = [];
  bool _isLoading = true;
  bool _hasPermission = false;

  @override
  void initState() {
    super.initState();
    _requestPermissionAndLoadMedia();
  }

  /// Request storage permissions and load media files
  Future<void> _requestPermissionAndLoadMedia() async {
    try {
      // Request permissions
      final PermissionState ps = await PhotoManager.requestPermissionExtend();
      if (ps.isAuth) {
        setState(() {
          _hasPermission = true;
        });
        await _loadMedia();
      } else {
        setState(() {
          _hasPermission = false;
          _isLoading = false;
        });
        _showPermissionDialog();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasPermission = false;
      });
      _showErrorDialog('Error requesting permissions: $e');
    }
  }

  /// Load all media files from device storage
  Future<void> _loadMedia() async {
    try {
      // Get all albums
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.common, // Gets both images and videos
      );

      if (albums.isNotEmpty) {
        // Get all assets from the first album (usually "Recent" or "All")
        final List<AssetEntity> media = await albums[0].getAssetListPaged(
          page: 0,
          size: 1000, // Adjust based on your needs
        );

        setState(() {
          _mediaList = media;
          _isLoading = false;
        });
      } else {
        setState(() {
          _mediaList = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Error loading media: $e');
    }
  }

  /// Show permission dialog
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permission Required'),
        content: const Text(
          'This app needs access to your photos and videos to display them in the gallery.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gallery App Lite'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _requestPermissionAndLoadMedia,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (!_hasPermission) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.photo_library_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Permission Required',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please grant storage permission to view your photos and videos.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _requestPermissionAndLoadMedia,
              child: const Text('Grant Permission'),
            ),
          ],
        ),
      );
    }

    if (_mediaList.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No Media Found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'No photos or videos found on your device.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: _mediaList.length,
      itemBuilder: (context, index) {
        return _buildMediaItem(_mediaList[index]);
      },
    );
  }

  /// Build individual media item widget
  Widget _buildMediaItem(AssetEntity asset) {
    return GestureDetector(
      onTap: () => _openMedia(asset),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[300],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Thumbnail image
              FutureBuilder<Widget?>(
                future: _buildThumbnail(asset),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return snapshot.data!;
                  }
                  return const Center(child: CircularProgressIndicator());
                },
              ),
              // Video play icon overlay
              if (asset.type == AssetType.video)
                const Center(
                  child: Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              // Duration overlay for videos
              if (asset.type == AssetType.video)
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _formatDuration(asset.duration),
                      style: const TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build thumbnail for media item
  Future<Widget?> _buildThumbnail(AssetEntity asset) async {
    try {
      final thumbnail = await asset.thumbnailDataWithSize(
        const ThumbnailSize(200, 200),
      );
      if (thumbnail != null) {
        return Image.memory(thumbnail, fit: BoxFit.cover);
      }
    } catch (e) {
      debugPrint('Error loading thumbnail: $e');
    }
    return Container(
      color: Colors.grey[400],
      child: const Icon(Icons.broken_image, color: Colors.grey),
    );
  }

  /// Format duration for video display
  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final minutes = duration.inMinutes;
    final remainingSeconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Open media in full screen
  void _openMedia(AssetEntity asset) {
    final currentIndex = _mediaList.indexOf(asset);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MediaGalleryViewer(
          mediaList: _mediaList,
          initialIndex: currentIndex,
          onMediaDeleted: _onMediaDeleted,
        ),
      ),
    );
  }

  /// Handle media deletion callback
  void _onMediaDeleted(AssetEntity deletedAsset) {
    setState(() {
      _mediaList.remove(deletedAsset);
    });
  }
}

/// Enhanced Media Gallery Viewer with swipe navigation and advanced features
class MediaGalleryViewer extends StatefulWidget {
  final List<AssetEntity> mediaList;
  final int initialIndex;
  final Function(AssetEntity) onMediaDeleted;

  const MediaGalleryViewer({
    super.key,
    required this.mediaList,
    required this.initialIndex,
    required this.onMediaDeleted,
  });

  @override
  State<MediaGalleryViewer> createState() => _MediaGalleryViewerState();
}

class _MediaGalleryViewerState extends State<MediaGalleryViewer> {
  late PageController _pageController;
  late int _currentIndex;
  bool _showAppBar = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  AssetEntity get _currentAsset => widget.mediaList[_currentIndex];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _showAppBar ? _buildAppBar() : null,
      body: Stack(
        children: [
          _buildPageView(),
          if (_showAppBar) _buildBottomControls(),
          if (_isLoading) _buildLoadingOverlay(),
        ],
      ),
    );
  }

  /// Build app bar with media info and actions
  PreferredSizeWidget _buildAppBar() {
    final asset = _currentAsset;
    final isVideo = asset.type == AssetType.video;

    return AppBar(
      backgroundColor: Colors.black.withOpacity(0.7),
      foregroundColor: Colors.white,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_currentIndex + 1} of ${widget.mediaList.length}',
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            isVideo ? 'Video' : 'Image',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _shareMedia,
          icon: const Icon(Icons.share),
          tooltip: 'Share',
        ),
        IconButton(
          onPressed: _showMediaInfo,
          icon: const Icon(Icons.info_outline),
          tooltip: 'Info',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'details',
              child: Row(
                children: [
                  Icon(Icons.info),
                  SizedBox(width: 8),
                  Text('Details'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build page view for swiping between media
  Widget _buildPageView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showAppBar = !_showAppBar;
        });
      },
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.mediaList.length,
        itemBuilder: (context, index) {
          final asset = widget.mediaList[index];
          return _buildMediaWidget(asset);
        },
      ),
    );
  }

  /// Build media widget (image or video)
  Widget _buildMediaWidget(AssetEntity asset) {
    if (asset.type == AssetType.image) {
      return _buildImageWidget(asset);
    } else if (asset.type == AssetType.video) {
      return _buildVideoWidget(asset);
    }
    return const Center(
      child: Icon(Icons.error, color: Colors.white, size: 64),
    );
  }

  /// Build image widget with zoom and pan
  Widget _buildImageWidget(AssetEntity asset) {
    return FutureBuilder<Widget?>(
      future: _buildFullImage(asset),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return InteractiveViewer(
            panEnabled: true,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 4.0,
            child: snapshot.data!,
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  /// Build full resolution image
  Future<Widget?> _buildFullImage(AssetEntity asset) async {
    try {
      final file = await asset.file;
      if (file != null) {
        return Image.file(
          file,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Icon(Icons.broken_image, color: Colors.white, size: 64),
            );
          },
        );
      }
    } catch (e) {
      debugPrint('Error loading full image: $e');
    }
    return const Center(
      child: Icon(Icons.broken_image, color: Colors.white, size: 64),
    );
  }

  /// Build enhanced video widget
  Widget _buildVideoWidget(AssetEntity asset) {
    return EnhancedVideoPlayer(asset: asset);
  }

  /// Build bottom controls
  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.transparent, Colors.black.withValues(alpha: 0.7)],
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              onPressed: _currentIndex > 0 ? _previousMedia : null,
              icon: const Icon(Icons.skip_previous, color: Colors.white),
              iconSize: 32,
            ),
            IconButton(
              onPressed: _togglePlayPause,
              icon: Icon(
                _currentAsset.type == AssetType.video
                    ? Icons.play_circle_outline
                    : Icons.zoom_in,
                color: Colors.white,
              ),
              iconSize: 32,
            ),
            IconButton(
              onPressed: _currentIndex < widget.mediaList.length - 1
                  ? _nextMedia
                  : null,
              icon: const Icon(Icons.skip_next, color: Colors.white),
              iconSize: 32,
            ),
          ],
        ),
      ),
    );
  }

  /// Build loading overlay
  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  /// Navigate to previous media
  void _previousMedia() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Navigate to next media
  void _nextMedia() {
    if (_currentIndex < widget.mediaList.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Toggle play/pause for videos or zoom for images
  void _togglePlayPause() {
    // This will be handled by the video player widget
    // For images, we could implement a zoom toggle
  }

  /// Share current media
  void _shareMedia() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  /// Show media information
  void _showMediaInfo() {
    final asset = _currentAsset;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Media Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${asset.type == AssetType.image ? 'Image' : 'Video'}'),
            Text('Size: ${asset.width} x ${asset.height}'),
            if (asset.type == AssetType.video)
              Text('Duration: ${_formatDuration(asset.duration)}'),
            Text(
              'Date: ${asset.createDateTime?.toString().split(' ')[0] ?? 'Unknown'}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'delete':
        _confirmDelete();
        break;
      case 'details':
        _showMediaInfo();
        break;
    }
  }

  /// Confirm deletion of current media
  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Media'),
        content: Text(
          'Are you sure you want to delete this ${_currentAsset.type == AssetType.image ? 'image' : 'video'}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteCurrentMedia();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Delete current media
  void _deleteCurrentMedia() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final asset = _currentAsset;

      // Remove from local list
      widget.onMediaDeleted(asset);

      // If this was the last item, go back
      if (widget.mediaList.length <= 1) {
        Navigator.pop(context);
        return;
      }

      // Adjust current index if needed
      if (_currentIndex >= widget.mediaList.length) {
        _currentIndex = widget.mediaList.length - 1;
      }

      // Navigate to the adjusted index
      _pageController.animateToPage(
        _currentIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Media deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error deleting media: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Format duration for display
  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final minutes = duration.inMinutes;
    final remainingSeconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

/// Enhanced Video Player with advanced controls
class EnhancedVideoPlayer extends StatefulWidget {
  final AssetEntity asset;

  const EnhancedVideoPlayer({super.key, required this.asset});

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  bool _showControls = true;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  /// Initialize video player
  Future<void> _initializeVideo() async {
    try {
      final file = await widget.asset.file;
      if (file != null) {
        _controller = VideoPlayerController.file(file);
        await _controller!.initialize();

        _controller!.addListener(_videoListener);

        setState(() {
          _isLoading = false;
          _duration = _controller!.value.duration;
        });
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing video: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// Video listener for position updates
  void _videoListener() {
    if (_controller != null) {
      setState(() {
        _position = _controller!.value.position;
        _isPlaying = _controller!.value.isPlaying;
      });
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError || _controller == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 64),
            SizedBox(height: 16),
            Text('Error loading video', style: TextStyle(color: Colors.white)),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Center(
            child: AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: VideoPlayer(_controller!),
            ),
          ),
          if (_showControls) _buildVideoControls(),
        ],
      ),
    );
  }

  /// Build enhanced video controls
  Widget _buildVideoControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Top controls
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  _formatDuration(_position.inSeconds),
                  style: const TextStyle(color: Colors.white),
                ),
                const Text(' / ', style: TextStyle(color: Colors.white)),
                Text(
                  _formatDuration(_duration.inSeconds),
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),

          // Center play/pause button
          Container(
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _togglePlayPause,
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 48,
              ),
            ),
          ),

          // Bottom controls
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Progress bar
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Colors.white,
                    inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                    thumbColor: Colors.white,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 8,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 16,
                    ),
                  ),
                  child: Slider(
                    value: _position.inSeconds.toDouble(),
                    max: _duration.inSeconds.toDouble(),
                    onChanged: _onSeek,
                  ),
                ),

                // Control buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      onPressed: _rewind,
                      icon: const Icon(Icons.replay_10, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: _togglePlayPause,
                      icon: Icon(
                        _isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    IconButton(
                      onPressed: _fastForward,
                      icon: const Icon(Icons.forward_10, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: _toggleMute,
                      icon: Icon(
                        _controller!.value.volume > 0
                            ? Icons.volume_up
                            : Icons.volume_off,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Toggle play/pause
  void _togglePlayPause() {
    if (_controller != null) {
      if (_isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
    }
  }

  /// Seek to position
  void _onSeek(double value) {
    if (_controller != null) {
      final position = Duration(seconds: value.toInt());
      _controller!.seekTo(position);
    }
  }

  /// Rewind 10 seconds
  void _rewind() {
    if (_controller != null) {
      final newPosition = _position - const Duration(seconds: 10);
      _controller!.seekTo(
        newPosition < Duration.zero ? Duration.zero : newPosition,
      );
    }
  }

  /// Fast forward 10 seconds
  void _fastForward() {
    if (_controller != null) {
      final newPosition = _position + const Duration(seconds: 10);
      _controller!.seekTo(newPosition > _duration ? _duration : newPosition);
    }
  }

  /// Toggle mute
  void _toggleMute() {
    if (_controller != null) {
      _controller!.setVolume(_controller!.value.volume > 0 ? 0.0 : 1.0);
    }
  }

  /// Format duration for display
  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final remainingSeconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
}
